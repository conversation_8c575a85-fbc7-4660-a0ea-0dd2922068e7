# Weather Station Page
st.markdown('<h1 class="main-header">🌤️ Weather Station Dashboard</h1>', unsafe_allow_html=True)

st.markdown("""
<div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
    <h3>🌦️ Real-time Weather Intelligence for Smart Farming</h3>
    <p>Monitor current conditions and forecasts to make informed agricultural decisions.</p>
</div>
""", unsafe_allow_html=True)

# Current weather conditions
st.markdown("### 🌡️ Current Conditions")

# Generate realistic weather data
current_temp = random.randint(18, 32)
humidity = random.randint(45, 85)
wind_speed = random.randint(5, 25)
pressure = random.randint(1010, 1025)
uv_index = random.randint(3, 9)

col1, col2, col3, col4, col5 = st.columns(5)

with col1:
    st.metric(
        label="🌡️ Temperature",
        value=f"{current_temp}°C",
        delta=f"{random.randint(-3, 3)}°C from yesterday"
    )

with col2:
    st.metric(
        label="💧 Humidity",
        value=f"{humidity}%",
        delta=f"{random.randint(-10, 10)}%" if random.choice([True, False]) else None
    )

with col3:
    st.metric(
        label="💨 Wind Speed",
        value=f"{wind_speed} km/h",
        delta="Calm" if wind_speed < 10 else "Breezy" if wind_speed < 20 else "Windy"
    )

with col4:
    st.metric(
        label="🔽 Pressure",
        value=f"{pressure} hPa",
        delta="Stable" if 1013 <= pressure <= 1020 else "Changing"
    )

with col5:
    st.metric(
        label="☀️ UV Index",
        value=f"{uv_index}",
        delta="Low" if uv_index < 3 else "Moderate" if uv_index < 6 else "High"
    )

# Weather alerts and recommendations
st.markdown("---")
st.markdown("### 🚨 Weather Alerts & Farming Recommendations")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### ⚠️ Active Alerts")
    
    # Generate weather alerts based on conditions
    alerts = []
    
    if humidity > 80:
        alerts.append(("🌧️ High Humidity Alert", "Risk of fungal diseases. Improve ventilation.", "warning"))
    
    if wind_speed > 20:
        alerts.append(("💨 Strong Wind Warning", "Secure greenhouse structures and young plants.", "error"))
    
    if uv_index > 7:
        alerts.append(("☀️ High UV Alert", "Provide shade for sensitive crops.", "info"))
    
    if current_temp > 30:
        alerts.append(("🌡️ Heat Warning", "Increase irrigation frequency.", "warning"))
    
    if not alerts:
        st.success("✅ No active weather alerts. Conditions are favorable for farming activities.")
    else:
        for title, message, alert_type in alerts:
            if alert_type == "warning":
                st.warning(f"**{title}**\n{message}")
            elif alert_type == "error":
                st.error(f"**{title}**\n{message}")
            else:
                st.info(f"**{title}**\n{message}")

with col2:
    st.markdown("#### 💡 Today's Recommendations")
    
    recommendations = []
    
    if 20 <= current_temp <= 25 and 50 <= humidity <= 70:
        recommendations.append("🌱 Perfect conditions for planting!")
    
    if humidity < 50:
        recommendations.append("💧 Increase irrigation - low humidity detected")
    
    if wind_speed < 5:
        recommendations.append("🌬️ Good day for pesticide application")
    
    if uv_index > 6:
        recommendations.append("🏠 Consider shade cloth for sensitive plants")
    
    if current_temp < 20:
        recommendations.append("🔥 Consider frost protection measures")
    
    if not recommendations:
        recommendations.append("📊 Monitor conditions throughout the day")
    
    for rec in recommendations:
        st.write(f"• {rec}")

# 7-day forecast
st.markdown("---")
st.markdown("### 📅 7-Day Forecast")

# Generate forecast data
forecast_days = ['Today', 'Tomorrow', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7']
forecast_data = []

for i, day in enumerate(forecast_days):
    temp_high = current_temp + random.randint(-5, 5)
    temp_low = temp_high - random.randint(5, 12)
    rain_chance = random.randint(0, 100)
    
    weather_conditions = ['☀️ Sunny', '⛅ Partly Cloudy', '☁️ Cloudy', '🌧️ Rainy', '⛈️ Thunderstorms']
    condition = random.choice(weather_conditions)
    
    forecast_data.append({
        'Day': day,
        'High': temp_high,
        'Low': temp_low,
        'Condition': condition,
        'Rain %': rain_chance
    })

forecast_df = pd.DataFrame(forecast_data)

# Display forecast in columns
cols = st.columns(7)
for i, (_, row) in enumerate(forecast_df.iterrows()):
    with cols[i]:
        st.markdown(f"""
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; text-align: center; margin: 0.2rem;">
            <h4>{row['Day']}</h4>
            <p>{row['Condition']}</p>
            <p><strong>{row['High']}°/{row['Low']}°</strong></p>
            <p>💧 {row['Rain %']}%</p>
        </div>
        """, unsafe_allow_html=True)

# Historical weather data
st.markdown("---")
st.markdown("### 📊 Weather Trends")

# Generate historical data
dates = pd.date_range(start='2024-08-01', end='2024-09-20', freq='D')
np.random.seed(42)

weather_history = pd.DataFrame({
    'Date': dates,
    'Temperature': np.random.normal(25, 5, len(dates)),
    'Humidity': np.random.normal(65, 15, len(dates)),
    'Rainfall': np.random.exponential(2, len(dates))
})

# Ensure realistic ranges
weather_history['Temperature'] = np.clip(weather_history['Temperature'], 15, 35)
weather_history['Humidity'] = np.clip(weather_history['Humidity'], 30, 95)
weather_history['Rainfall'] = np.clip(weather_history['Rainfall'], 0, 50)

# Create tabs for different charts
chart_tabs = st.tabs(["🌡️ Temperature", "💧 Humidity", "🌧️ Rainfall"])

with chart_tabs[0]:
    fig = px.line(weather_history, x='Date', y='Temperature', 
                  title="Temperature Trends (°C)",
                  line_shape='spline')
    fig.add_hline(y=25, line_dash="dash", line_color="green", 
                  annotation_text="Optimal Growing Temperature")
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

with chart_tabs[1]:
    fig = px.line(weather_history, x='Date', y='Humidity', 
                  title="Humidity Levels (%)",
                  line_shape='spline')
    fig.add_hline(y=60, line_dash="dash", line_color="blue", 
                  annotation_text="Optimal Humidity")
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

with chart_tabs[2]:
    fig = px.bar(weather_history, x='Date', y='Rainfall', 
                 title="Daily Rainfall (mm)")
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

# Irrigation recommendations
st.markdown("---")
st.markdown("### 💧 Smart Irrigation Recommendations")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### 🕐 Optimal Watering Times")
    
    # Calculate best watering times based on weather
    morning_score = 85 if current_temp < 25 else 95
    afternoon_score = 30 if current_temp > 28 else 60
    evening_score = 75 if humidity < 70 else 50
    
    times_data = pd.DataFrame({
        'Time': ['6:00 AM', '12:00 PM', '6:00 PM'],
        'Score': [morning_score, afternoon_score, evening_score],
        'Recommendation': ['Excellent', 'Poor', 'Good']
    })
    
    fig = px.bar(times_data, x='Time', y='Score', color='Recommendation',
                 title="Watering Time Effectiveness",
                 color_discrete_map={'Excellent': '#4CAF50', 'Good': '#FFC107', 'Poor': '#F44336'})
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)

with col2:
    st.markdown("#### 📊 Water Requirements")
    
    # Calculate water needs based on weather
    base_water = 10  # mm per day
    temp_factor = 1 + (current_temp - 20) * 0.05
    humidity_factor = 1 - (humidity - 50) * 0.01
    wind_factor = 1 + wind_speed * 0.02
    
    water_need = base_water * temp_factor * humidity_factor * wind_factor
    water_need = max(5, min(25, water_need))  # Clamp between 5-25mm
    
    st.metric("💧 Daily Water Requirement", f"{water_need:.1f} mm", 
              f"{'↑' if water_need > 12 else '↓'} vs normal")
    
    # Water schedule
    st.markdown("**Recommended Schedule:**")
    if water_need > 15:
        st.write("• 🌅 Morning: 60% of daily amount")
        st.write("• 🌆 Evening: 40% of daily amount")
    else:
        st.write("• 🌅 Morning: 100% of daily amount")
    
    st.markdown(f"**Next watering:** {6 if water_need > 15 else 7}:00 AM")

# Weather-based crop activities
st.markdown("---")
st.markdown("### 🌾 Weather-Based Activity Planner")

activities_col1, activities_col2 = st.columns(2)

with activities_col1:
    st.markdown("#### ✅ Recommended Activities")
    
    good_activities = []
    
    if wind_speed < 10:
        good_activities.append("🚁 Drone spraying operations")
    
    if humidity < 60 and current_temp < 28:
        good_activities.append("🌱 Transplanting seedlings")
    
    if 18 <= current_temp <= 25:
        good_activities.append("🌾 Harvesting activities")
    
    if humidity > 70:
        good_activities.append("🔍 Disease monitoring")
    
    if not good_activities:
        good_activities.append("📊 Data collection and planning")
    
    for activity in good_activities:
        st.success(activity)

with activities_col2:
    st.markdown("#### ❌ Activities to Avoid")
    
    avoid_activities = []
    
    if wind_speed > 15:
        avoid_activities.append("🚁 Aerial applications")
    
    if current_temp > 30:
        avoid_activities.append("🌱 Planting sensitive crops")
    
    if humidity > 85:
        avoid_activities.append("💧 Overhead irrigation")
    
    if uv_index > 8:
        avoid_activities.append("🌞 Midday field work")
    
    if not avoid_activities:
        avoid_activities.append("No restrictions today!")
    
    for activity in avoid_activities:
        if activity == "No restrictions today!":
            st.success(activity)
        else:
            st.error(activity)

# Quick actions
st.markdown("---")
st.markdown("### ⚡ Quick Actions")

col1, col2, col3, col4 = st.columns(4)

with col1:
    if st.button("📱 Weather Alerts", use_container_width=True):
        st.info("🔔 Weather alerts configured!")

with col2:
    if st.button("💧 Auto Irrigation", use_container_width=True):
        st.info("🤖 Smart irrigation activated!")

with col3:
    if st.button("📊 Weather Report", use_container_width=True):
        st.info("📄 Detailed weather report generated!")

with col4:
    if st.button("🌾 Activity Plan", use_container_width=True):
        st.info("📅 Weekly activity plan created!")
