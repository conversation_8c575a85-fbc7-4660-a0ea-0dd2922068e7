# 🌾 AgriBot - Autonomous Decision System for Farmers

A comprehensive prototype of an autonomous decision-making system designed to help farmers manage their agricultural operations remotely. This system features a supervisor agent that coordinates multiple specialized sub-agents to provide intelligent farming recommendations and automated control of farming equipment.

## 🚀 Features

### 🏠 Home Dashboard
- **Real-time System Status**: Monitor irrigation systems, drone sprayers, and overall system health
- **Quick Actions**: Instant access to land checks, crop recommendations, weather updates, and disease detection
- **Agent Network Status**: Live monitoring of all AI agents in the system
- **Interactive Chat**: Direct communication with the Supervisor Agent for personalized farming advice

### ⚗️ Soil Parameters Configuration
- **NPK Level Management**: Set and monitor Nitrogen, Phosphorus, and Potassium levels
- **pH Control**: Optimize soil acidity for different crops
- **Visual Analytics**: Radar charts and trend analysis for soil health
- **Smart Recommendations**: AI-powered crop suitability and fertilizer suggestions
- **Historical Tracking**: Monitor soil parameter changes over time

### 📸 Plant Health Monitoring
- **AI-Powered Disease Detection**: Upload plant images for instant health analysis
- **Disease Library**: Comprehensive database of common plant diseases and treatments
- **Health Scoring**: Quantitative plant health assessment with visual indicators
- **Treatment Plans**: Automated generation of treatment recommendations
- **Progress Tracking**: Monitor plant health improvements over time

### 🌤️ Weather Station
- **Real-time Weather Data**: Current conditions including temperature, humidity, wind, and UV index
- **7-Day Forecast**: Extended weather predictions for farming planning
- **Weather Alerts**: Automated notifications for adverse weather conditions
- **Irrigation Recommendations**: Smart watering schedules based on weather patterns
- **Activity Planning**: Weather-based suggestions for optimal farming activities

## 🤖 AI Agent System

### Supervisor Agent
The central intelligence that coordinates all farming operations and communicates with farmers through natural language chat interface.

### Sub-Agents
- **🌱 Crop Recommendation Agent**: Analyzes soil and weather conditions to suggest optimal crops
- **🔬 Disease Detection Agent**: Processes plant images to identify diseases and pests
- **🌤️ Weather Agent**: Provides real-time weather data and forecasts
- **🧪 Fertility Check Agent**: Monitors and analyzes soil nutrient levels
- **💧 Irrigation Agent**: Manages automated watering systems
- **🚁 Drone Control Agent**: Coordinates aerial spraying and monitoring operations

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Quick Start

1. **Clone or download the project files**
   ```bash
   # If using git
   git clone <repository-url>
   cd agribot-prototype
   
   # Or simply download and extract the files to a folder
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   streamlit run app.py
   ```

4. **Access the dashboard**
   - Open your web browser
   - Navigate to `http://localhost:8501`
   - Start exploring the AgriBot dashboard!

### Alternative Installation (using virtual environment)
```bash
# Create virtual environment
python -m venv agribot_env

# Activate virtual environment
# On Windows:
agribot_env\Scripts\activate
# On macOS/Linux:
source agribot_env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run the application
streamlit run app.py
```

## 📱 Usage Guide

### Getting Started
1. **Home Dashboard**: Start here to get an overview of your farm's status
2. **System Controls**: Toggle irrigation and drone systems on/off
3. **Quick Actions**: Use buttons for instant analysis and recommendations
4. **Chat Interface**: Ask the Supervisor Agent questions in natural language

### Setting Up Soil Parameters
1. Navigate to "⚗️ Soil Parameters"
2. Adjust NPK levels and pH using the sliders
3. View real-time recommendations based on your settings
4. Monitor historical trends and optimization suggestions

### Plant Health Monitoring
1. Go to "📸 Plant Monitoring"
2. Upload clear images of your plants
3. Run health analysis and disease detection
4. Follow treatment recommendations provided by the AI

### Weather Monitoring
1. Visit "🌤️ Weather Station"
2. Check current conditions and 7-day forecast
3. Review weather alerts and farming recommendations
4. Plan activities based on weather-optimized suggestions

## 🎯 Example Interactions

### Chat with Supervisor Agent
- "Check my land status"
- "Recommend crops for this season"
- "What's the weather forecast?"
- "Detect diseases in my plants"
- "Should I irrigate today?"
- "Check soil fertility levels"

### System Responses
The Supervisor Agent provides detailed, actionable responses by coordinating with specialized sub-agents to deliver comprehensive farming insights.

## 🔧 Customization

### Adding New Crops
Modify the `get_crop_recommendation()` function in `app.py` to include additional crop varieties with their specific requirements.

### Extending Disease Database
Update the disease detection system by adding new diseases to the `get_disease_analysis()` function and corresponding treatment protocols.

### Weather Integration
For production use, replace the simulated weather data with real API calls to weather services like OpenWeatherMap or AccuWeather.

## 📊 Technical Architecture

### Frontend
- **Streamlit**: Modern web interface with responsive design
- **Plotly**: Interactive charts and visualizations
- **Custom CSS**: Professional styling and animations

### Backend Simulation
- **Agent System**: Modular AI agents for different farming aspects
- **Data Management**: Session state management for user preferences
- **Mock APIs**: Simulated responses for prototype demonstration

### File Structure
```
agribot-prototype/
├── app.py                 # Main application file
├── pages/
│   ├── soil_parameters.py # Soil management interface
│   ├── plant_monitoring.py# Plant health monitoring
│   └── weather_station.py # Weather dashboard
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🚀 Future Enhancements

### Production Features
- **Real API Integration**: Connect to actual weather, soil, and IoT sensors
- **Database Storage**: Persistent data storage for historical analysis
- **User Authentication**: Multi-user support with role-based access
- **Mobile App**: Native mobile application for field use
- **Hardware Integration**: Direct control of irrigation systems and drones

### Advanced AI Features
- **Computer Vision**: Enhanced plant disease detection using deep learning
- **Predictive Analytics**: Crop yield prediction and market analysis
- **Automated Decision Making**: Fully autonomous farming operations
- **IoT Integration**: Real-time sensor data from field devices

## 🤝 Contributing

This is a prototype system designed for demonstration purposes. For production deployment, consider:

1. **Security**: Implement proper authentication and data encryption
2. **Scalability**: Use cloud infrastructure for multiple farm management
3. **Real Data**: Integrate with actual farming equipment and sensors
4. **Testing**: Comprehensive testing with real farming scenarios

## 📞 Support

For questions about this prototype or suggestions for improvements:
- Review the code comments for implementation details
- Test different scenarios using the chat interface
- Experiment with various soil and weather conditions
- Explore all dashboard features and agent interactions

## 📄 License

This prototype is provided for educational and demonstration purposes. Please ensure compliance with relevant agricultural and data protection regulations when adapting for production use.

---

**🌾 Happy Farming with AgriBot! 🤖**
