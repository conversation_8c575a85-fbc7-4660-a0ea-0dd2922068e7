# Soil Parameters Configuration Page
st.markdown('<h1 class="main-header">⚗️ Soil Parameters Configuration</h1>', unsafe_allow_html=True)

st.markdown("""
<div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
    <h3>🌱 Optimize Your Soil for Maximum Yield</h3>
    <p>Configure NPK levels and pH values to ensure optimal growing conditions for your crops.</p>
</div>
""", unsafe_allow_html=True)

# Current soil status overview
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric(
        label="Nitrogen (N)",
        value=f"{st.session_state.npk_values['N']} ppm",
        delta="Optimal" if 40 <= st.session_state.npk_values['N'] <= 60 else "Adjust needed"
    )

with col2:
    st.metric(
        label="Phosphorus (P)",
        value=f"{st.session_state.npk_values['P']} ppm",
        delta="Optimal" if 25 <= st.session_state.npk_values['P'] <= 35 else "Adjust needed"
    )

with col3:
    st.metric(
        label="Potassium (K)",
        value=f"{st.session_state.npk_values['K']} ppm",
        delta="Optimal" if 35 <= st.session_state.npk_values['K'] <= 45 else "Adjust needed"
    )

with col4:
    st.metric(
        label="pH Level",
        value=f"{st.session_state.npk_values['pH']}",
        delta="Optimal" if 6.0 <= st.session_state.npk_values['pH'] <= 7.0 else "Adjust needed"
    )

st.markdown("---")

# NPK and pH Configuration
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("### 🔧 Adjust Soil Parameters")
    
    # Nitrogen slider
    nitrogen = st.slider(
        "Nitrogen (N) - ppm",
        min_value=0,
        max_value=100,
        value=st.session_state.npk_values['N'],
        help="Nitrogen promotes leaf growth and green color. Optimal range: 40-60 ppm"
    )
    
    # Phosphorus slider
    phosphorus = st.slider(
        "Phosphorus (P) - ppm",
        min_value=0,
        max_value=60,
        value=st.session_state.npk_values['P'],
        help="Phosphorus supports root development and flowering. Optimal range: 25-35 ppm"
    )
    
    # Potassium slider
    potassium = st.slider(
        "Potassium (K) - ppm",
        min_value=0,
        max_value=80,
        value=st.session_state.npk_values['K'],
        help="Potassium improves disease resistance and fruit quality. Optimal range: 35-45 ppm"
    )
    
    # pH slider
    ph_level = st.slider(
        "pH Level",
        min_value=4.0,
        max_value=9.0,
        value=st.session_state.npk_values['pH'],
        step=0.1,
        help="pH affects nutrient availability. Optimal range: 6.0-7.0"
    )
    
    # Update button
    if st.button("💾 Update Soil Parameters", use_container_width=True):
        st.session_state.npk_values = {
            'N': nitrogen,
            'P': phosphorus,
            'K': potassium,
            'pH': ph_level
        }
        st.success("✅ Soil parameters updated successfully!")
        st.balloons()
        time.sleep(1)
        st.rerun()

with col2:
    st.markdown("### 📊 Soil Health Visualization")
    
    # Create radar chart for NPK levels
    categories = ['Nitrogen', 'Phosphorus', 'Potassium']
    current_values = [
        st.session_state.npk_values['N'],
        st.session_state.npk_values['P'],
        st.session_state.npk_values['K']
    ]
    optimal_values = [50, 30, 40]  # Optimal ranges
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=current_values,
        theta=categories,
        fill='toself',
        name='Current Levels',
        line_color='#2E8B57'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=optimal_values,
        theta=categories,
        fill='toself',
        name='Optimal Levels',
        line_color='#FFD700',
        opacity=0.6
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=True,
        title="NPK Levels Comparison",
        height=300
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # pH indicator
    ph_color = "#4CAF50" if 6.0 <= st.session_state.npk_values['pH'] <= 7.0 else "#FF6B6B"
    st.markdown(f"""
    <div style="background: {ph_color}; color: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <h4>pH Status</h4>
        <h2>{st.session_state.npk_values['pH']}</h2>
        <p>{'Optimal' if 6.0 <= st.session_state.npk_values['pH'] <= 7.0 else 'Needs Adjustment'}</p>
    </div>
    """, unsafe_allow_html=True)

st.markdown("---")

# Recommendations section
st.markdown("### 💡 Smart Recommendations")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### 🌾 Crop Suitability")
    
    # Determine crop recommendations based on current values
    n, p, k, ph = st.session_state.npk_values['N'], st.session_state.npk_values['P'], st.session_state.npk_values['K'], st.session_state.npk_values['pH']
    
    recommendations = []
    
    if 40 <= n <= 60 and 25 <= p <= 35 and 35 <= k <= 45 and 6.0 <= ph <= 7.0:
        recommendations = ["🍅 Tomatoes", "🥬 Lettuce", "🥕 Carrots", "🌶️ Peppers"]
        status = "Excellent conditions for most crops!"
    elif n > 60:
        recommendations = ["🌽 Corn", "🥬 Leafy Greens"]
        status = "High nitrogen - good for leafy crops"
    elif p > 35:
        recommendations = ["🌻 Sunflowers", "🌸 Flowering plants"]
        status = "High phosphorus - excellent for flowering"
    else:
        recommendations = ["🥔 Potatoes", "🧄 Onions"]
        status = "Consider soil amendments"
    
    st.info(status)
    for crop in recommendations:
        st.write(f"✅ {crop}")

with col2:
    st.markdown("#### 🧪 Fertilizer Recommendations")
    
    fertilizer_needs = []
    
    if n < 40:
        fertilizer_needs.append("🔵 Nitrogen fertilizer needed")
    elif n > 60:
        fertilizer_needs.append("🔵 Reduce nitrogen application")
    
    if p < 25:
        fertilizer_needs.append("🟡 Phosphorus supplement required")
    elif p > 35:
        fertilizer_needs.append("🟡 Reduce phosphorus")
    
    if k < 35:
        fertilizer_needs.append("🔴 Potassium boost needed")
    elif k > 45:
        fertilizer_needs.append("🔴 Reduce potassium")
    
    if ph < 6.0:
        fertilizer_needs.append("🟢 Add lime to increase pH")
    elif ph > 7.0:
        fertilizer_needs.append("🟢 Add sulfur to decrease pH")
    
    if not fertilizer_needs:
        st.success("🎉 Perfect balance! No adjustments needed.")
    else:
        for need in fertilizer_needs:
            st.write(need)

# Historical data simulation
st.markdown("---")
st.markdown("### 📈 Historical Trends")

# Generate sample historical data
dates = pd.date_range(start='2024-01-01', end='2024-09-20', freq='W')
np.random.seed(42)

historical_data = pd.DataFrame({
    'Date': dates,
    'Nitrogen': np.random.normal(50, 10, len(dates)),
    'Phosphorus': np.random.normal(30, 5, len(dates)),
    'Potassium': np.random.normal(40, 8, len(dates)),
    'pH': np.random.normal(6.5, 0.3, len(dates))
})

# Plot historical trends
fig = px.line(historical_data, x='Date', y=['Nitrogen', 'Phosphorus', 'Potassium'], 
              title="NPK Levels Over Time",
              labels={'value': 'Concentration (ppm)', 'variable': 'Nutrient'})
fig.update_layout(height=400)
st.plotly_chart(fig, use_container_width=True)

# Quick actions
st.markdown("### ⚡ Quick Actions")
col1, col2, col3 = st.columns(3)

with col1:
    if st.button("🔄 Auto-Optimize", use_container_width=True):
        st.session_state.npk_values = {'N': 50, 'P': 30, 'K': 40, 'pH': 6.5}
        st.success("Parameters optimized to recommended levels!")
        time.sleep(1)
        st.rerun()

with col2:
    if st.button("📊 Generate Report", use_container_width=True):
        st.info("Soil analysis report generated and sent to your dashboard!")

with col3:
    if st.button("🚨 Alert Setup", use_container_width=True):
        st.info("Alerts configured for parameter deviations!")
