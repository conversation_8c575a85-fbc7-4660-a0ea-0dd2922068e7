import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import random

# Page configuration
st.set_page_config(
    page_title="AgriBot - Autonomous Farming System",
    page_icon="🌾",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced Custom CSS for professional styling
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Headers */
    .main-header {
        font-family: 'Inter', sans-serif;
        font-size: 2.8rem;
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #1e3a8a 0%, #2E8B57 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Status Cards */
    .status-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }

    .status-on {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
    }

    .status-off {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
    }

    /* Agent Cards */
    .agent-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 5px solid #2E8B57;
        margin: 0.8rem 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(46, 139, 87, 0.1);
    }

    .agent-card:hover {
        transform: translateX(5px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        border-left-color: #1e40af;
    }

    /* Chat Messages */
    .chat-message {
        background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
        padding: 1rem;
        border-radius: 12px;
        margin: 0.8rem 0;
        border-left: 4px solid #0284c7;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        font-family: 'Inter', sans-serif;
    }

    /* Sidebar Styling */
    .sidebar-info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        border: 1px solid rgba(14, 165, 233, 0.2);
        box-shadow: 0 4px 16px rgba(0,0,0,0.05);
    }

    /* Button Enhancements */
    .stButton > button {
        background: linear-gradient(135deg, #2E8B57 0%, #1e40af 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        background: linear-gradient(135deg, #1e40af 0%, #2E8B57 100%);
    }

    /* Metric Cards */
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        transition: transform 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }

    /* Alert Styling */
    .stAlert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    /* Tabs Styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }

    .stTabs [data-baseweb="tab"] {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.1);
        padding: 0.5rem 1rem;
        font-weight: 500;
    }

    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #2E8B57 0%, #1e40af 100%);
        color: white;
    }

    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #2E8B57 0%, #1e40af 100%);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #1e40af 0%, #2E8B57 100%);
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'irrigation_status' not in st.session_state:
    st.session_state.irrigation_status = False
if 'drone_status' not in st.session_state:
    st.session_state.drone_status = False
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'npk_values' not in st.session_state:
    st.session_state.npk_values = {'N': 50, 'P': 30, 'K': 40, 'pH': 6.5}
if 'uploaded_image' not in st.session_state:
    st.session_state.uploaded_image = None

# Sidebar navigation
st.sidebar.markdown('<div class="sidebar-info"><h2>🌾 AgriBot Dashboard</h2><p>Autonomous Decision System for Smart Farming</p></div>', unsafe_allow_html=True)

page = st.sidebar.selectbox(
    "Navigate to:",
    ["🏠 Home Dashboard", "⚗️ Soil Parameters", "📸 Plant Monitoring", "🌤️ Weather Station"]
)

# Enhanced supervisor agent with sub-agent integration
def get_supervisor_response(user_input):
    user_input_lower = user_input.lower()

    if "check land" in user_input_lower or "land status" in user_input_lower:
        weather = get_weather_forecast()
        fertility = get_fertility_analysis()
        return f"🔍 **Land Analysis Complete!**\n\n📊 **Current Status:**\n• Soil moisture: 65%\n• Temperature: {weather['temp']}\n• Humidity: {weather['humidity']}\n• Soil fertility: {fertility['nitrogen_status']}\n\n💡 **Recommendation:** {fertility['recommendation']}"

    elif "crop recommendation" in user_input_lower or "recommend crop" in user_input_lower:
        crop = get_crop_recommendation()
        weather = get_weather_forecast()
        return f"🌱 **Crop Recommendation Agent Analysis:**\n\n🎯 **Recommended Crop:** {crop['name']}\n• Best season: {crop['season']}\n• Expected yield: {crop['yield']}\n• Water requirement: {crop['water']}\n• Current weather: {weather['today']}\n\n📅 **Optimal planting window:** Next 3-5 days"

    elif "weather" in user_input_lower:
        weather = get_weather_forecast()
        return f"🌤️ **Weather Agent Report:**\n\n☀️ **Current Conditions:**\n• Weather: {weather['today']}\n• Temperature: {weather['temp']}\n• Humidity: {weather['humidity']}\n• Rain chance: {weather['rain_chance']}\n\n🌾 **Farming Impact:** {'Perfect conditions for outdoor activities!' if weather['rain_chance'] == '10%' else 'Plan indoor activities if rain expected.'}"

    elif "disease" in user_input_lower or "health" in user_input_lower:
        disease = get_disease_analysis()
        return f"🔬 **Disease Detection Agent Analysis:**\n\n🦠 **Detected Issue:** {disease['name']}\n• Severity: {disease['severity']}\n• Confidence: {disease['confidence']}%\n• Treatment: {disease['treatment']}\n\n⚡ **Action Required:** {'Immediate attention needed!' if disease['severity'] == 'High' else 'Monitor and treat as recommended.'}"

    elif "irrigation" in user_input_lower or "water" in user_input_lower:
        weather = get_weather_forecast()
        return f"💧 **Irrigation System Status:**\n\n🚰 **Current Status:** {'Active' if st.session_state.irrigation_status else 'Inactive'}\n• Weather condition: {weather['today']}\n• Humidity: {weather['humidity']}\n• Rain probability: {weather['rain_chance']}\n\n⏰ **Optimal Schedule:** 6:00 AM and 6:00 PM daily"

    elif "fertilizer" in user_input_lower or "fertility" in user_input_lower:
        fertility = get_fertility_analysis()
        npk = st.session_state.npk_values
        return f"🧪 **Fertility Check Agent Report:**\n\n📊 **Current NPK Levels:**\n• Nitrogen: {npk['N']} ppm ({fertility['nitrogen_status']})\n• Phosphorus: {npk['P']} ppm ({fertility['phosphorus_status']})\n• Potassium: {npk['K']} ppm ({fertility['potassium_status']})\n• pH: {npk['pH']} ({fertility['ph_status']})\n\n💡 **Recommendation:** {fertility['recommendation']}"

    elif "drone" in user_input_lower:
        weather = get_weather_forecast()
        return f"🚁 **Drone Operations Status:**\n\n✈️ **Current Status:** {'Active' if st.session_state.drone_status else 'Inactive'}\n• Weather: {weather['today']}\n• Wind conditions: Suitable for operations\n• Visibility: Good\n\n🎯 **Ready for:** Spraying, monitoring, and surveillance missions"

    else:
        return f"🤖 **Supervisor Agent Ready!**\n\nI can help you with:\n• 🔍 Land status checks\n• 🌱 Crop recommendations  \n• 🌤️ Weather updates\n• 🔬 Disease detection\n• 💧 Irrigation management\n• 🧪 Soil fertility analysis\n• 🚁 Drone operations\n\nWhat would you like me to analyze?"

# Home Dashboard Page
def home_dashboard():
    st.markdown('<h1 class="main-header">🌾 AgriBot - Autonomous Farming Dashboard</h1>', unsafe_allow_html=True)
    
    # System Status Section
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("### 🚰 Irrigation System")
        status_class = "status-on" if st.session_state.irrigation_status else "status-off"
        status_text = "ACTIVE" if st.session_state.irrigation_status else "INACTIVE"
        st.markdown(f'<div class="status-card {status_class}"><h3>{status_text}</h3></div>', unsafe_allow_html=True)
        
        if st.button("Toggle Irrigation", key="irrigation_toggle"):
            st.session_state.irrigation_status = not st.session_state.irrigation_status
            st.rerun()
    
    with col2:
        st.markdown("### 🚁 Drone Sprayer")
        status_class = "status-on" if st.session_state.drone_status else "status-off"
        status_text = "ACTIVE" if st.session_state.drone_status else "INACTIVE"
        st.markdown(f'<div class="status-card {status_class}"><h3>{status_text}</h3></div>', unsafe_allow_html=True)
        
        if st.button("Toggle Drone", key="drone_toggle"):
            st.session_state.drone_status = not st.session_state.drone_status
            st.rerun()
    
    with col3:
        st.markdown("### 📊 System Health")
        st.markdown('<div class="status-card status-on"><h3>OPTIMAL</h3></div>', unsafe_allow_html=True)
        st.metric("Uptime", "99.8%", "0.2%")

    st.markdown("---")
    
    # Quick Actions Section
    st.markdown("### 🎯 Quick Actions")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔍 Check Land Status", use_container_width=True):
            response = get_supervisor_response("check land")
            st.session_state.chat_history.append(("System", response))
            st.success("Land check initiated!")
    
    with col2:
        if st.button("🌱 Recommend Crop", use_container_width=True):
            response = get_supervisor_response("crop recommendation")
            st.session_state.chat_history.append(("System", response))
            st.success("Crop analysis started!")
    
    with col3:
        if st.button("🌤️ Weather Update", use_container_width=True):
            response = get_supervisor_response("weather")
            st.session_state.chat_history.append(("System", response))
            st.success("Weather data refreshed!")
    
    with col4:
        if st.button("🔬 Disease Check", use_container_width=True):
            response = get_supervisor_response("disease check")
            st.session_state.chat_history.append(("System", response))
            st.success("Disease scan initiated!")

    st.markdown("---")
    
    # Agent Status Section
    st.markdown("### 🤖 Agent Network Status")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="agent-card">
            <h4>🧠 Supervisor Agent</h4>
            <p>Status: <span style="color: green;">●</span> Active</p>
            <p>Last Action: Land monitoring</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="agent-card">
            <h4>🌱 Crop Recommendation Agent</h4>
            <p>Status: <span style="color: green;">●</span> Active</p>
            <p>Last Action: Seasonal analysis</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="agent-card">
            <h4>🔬 Disease Detection Agent</h4>
            <p>Status: <span style="color: green;">●</span> Active</p>
            <p>Last Action: Image analysis</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="agent-card">
            <h4>🌤️ Weather Agent</h4>
            <p>Status: <span style="color: green;">●</span> Active</p>
            <p>Last Action: Forecast update</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("---")
    
    # Supervisor Agent Chat
    st.markdown("### 💬 Chat with Supervisor Agent")
    
    # Display chat history
    if st.session_state.chat_history:
        for sender, message in st.session_state.chat_history[-5:]:  # Show last 5 messages
            st.markdown(f'<div class="chat-message"><strong>{sender}:</strong> {message}</div>', unsafe_allow_html=True)
    
    # Chat input
    user_input = st.text_input("Ask the Supervisor Agent:", placeholder="e.g., 'Check soil moisture' or 'Recommend fertilizer'")
    
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("Send", use_container_width=True):
            if user_input:
                st.session_state.chat_history.append(("You", user_input))
                response = get_supervisor_response(user_input)
                st.session_state.chat_history.append(("Supervisor Agent", response))
                st.rerun()
    
    with col2:
        if st.button("Clear Chat", use_container_width=True):
            st.session_state.chat_history = []
            st.rerun()

# Enhanced agent responses with more detailed simulations
def get_crop_recommendation():
    """Simulate crop recommendation agent"""
    crops = [
        {"name": "Tomatoes", "season": "Spring-Summer", "yield": "High", "water": "Medium"},
        {"name": "Lettuce", "season": "Fall-Winter", "yield": "Medium", "water": "Low"},
        {"name": "Peppers", "season": "Summer", "yield": "High", "water": "Medium"},
        {"name": "Carrots", "season": "Fall", "yield": "Medium", "water": "Low"},
        {"name": "Spinach", "season": "Winter", "yield": "Medium", "water": "Low"}
    ]
    return random.choice(crops)

def get_disease_analysis():
    """Simulate disease detection agent"""
    diseases = [
        {"name": "Leaf Spot", "severity": "Low", "treatment": "Organic fungicide", "confidence": 85},
        {"name": "Powdery Mildew", "severity": "Medium", "treatment": "Improve ventilation", "confidence": 72},
        {"name": "Root Rot", "severity": "High", "treatment": "Reduce watering", "confidence": 91},
        {"name": "Aphid Infestation", "severity": "Low", "treatment": "Insecticidal soap", "confidence": 78}
    ]
    return random.choice(diseases)

def get_weather_forecast():
    """Simulate weather agent"""
    conditions = [
        {"today": "Sunny", "temp": "26°C", "humidity": "65%", "rain_chance": "10%"},
        {"today": "Partly Cloudy", "temp": "23°C", "humidity": "70%", "rain_chance": "30%"},
        {"today": "Rainy", "temp": "20°C", "humidity": "85%", "rain_chance": "80%"}
    ]
    return random.choice(conditions)

def get_fertility_analysis():
    """Simulate fertility check agent"""
    npk = st.session_state.npk_values
    analysis = {
        "nitrogen_status": "Optimal" if 40 <= npk['N'] <= 60 else "Needs adjustment",
        "phosphorus_status": "Optimal" if 25 <= npk['P'] <= 35 else "Needs adjustment",
        "potassium_status": "Optimal" if 35 <= npk['K'] <= 45 else "Needs adjustment",
        "ph_status": "Optimal" if 6.0 <= npk['pH'] <= 7.0 else "Needs adjustment",
        "recommendation": "Apply balanced fertilizer" if any(
            npk[k] < 40 if k == 'N' else npk[k] < 25 if k == 'P' else npk[k] < 35 if k == 'K' else npk[k] < 6.0 or npk[k] > 7.0
            for k in npk.keys()
        ) else "Soil conditions are optimal"
    }
    return analysis

# Main app logic
if page == "🏠 Home Dashboard":
    home_dashboard()
elif page == "⚗️ Soil Parameters":
    exec(open("pages/soil_parameters.py").read())
elif page == "📸 Plant Monitoring":
    exec(open("pages/plant_monitoring.py").read())
elif page == "🌤️ Weather Station":
    exec(open("pages/weather_station.py").read())
