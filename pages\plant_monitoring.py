# Plant Monitoring Page
st.markdown('<h1 class="main-header">📸 Plant Health Monitoring</h1>', unsafe_allow_html=True)

st.markdown("""
<div style="background: #f0f8e8; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
    <h3>🔬 AI-Powered Plant Disease Detection</h3>
    <p>Upload plant images for instant health analysis and disease detection using our advanced AI agents.</p>
</div>
""", unsafe_allow_html=True)

# Image upload section
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("### 📤 Upload Plant Image")
    
    uploaded_file = st.file_uploader(
        "Choose a plant image...",
        type=['png', 'jpg', 'jpeg'],
        help="Upload clear images of leaves, stems, or fruits for best analysis results"
    )
    
    if uploaded_file is not None:
        st.session_state.uploaded_image = uploaded_file
        
        # Display uploaded image
        st.image(uploaded_file, caption="Uploaded Plant Image", use_column_width=True)
        
        # Analysis buttons
        col_a, col_b, col_c = st.columns(3)
        
        with col_a:
            if st.button("🔍 Analyze Health", use_container_width=True):
                with st.spinner("Analyzing plant health..."):
                    time.sleep(2)  # Simulate processing
                    st.success("✅ Analysis complete!")
        
        with col_b:
            if st.button("🦠 Detect Diseases", use_container_width=True):
                with st.spinner("Scanning for diseases..."):
                    time.sleep(2)  # Simulate processing
                    st.success("✅ Disease scan complete!")
        
        with col_c:
            if st.button("💊 Get Treatment", use_container_width=True):
                with st.spinner("Generating treatment plan..."):
                    time.sleep(2)  # Simulate processing
                    st.success("✅ Treatment plan ready!")

with col2:
    st.markdown("### 📋 Quick Guidelines")
    st.markdown("""
    **📸 Best Photo Practices:**
    - Use natural lighting
    - Focus on affected areas
    - Include healthy parts for comparison
    - Multiple angles preferred
    
    **🎯 What We Detect:**
    - Leaf diseases
    - Pest damage
    - Nutrient deficiencies
    - Growth abnormalities
    """)

# Analysis Results Section
if st.session_state.uploaded_image is not None:
    st.markdown("---")
    st.markdown("### 🔬 Analysis Results")
    
    # Simulate analysis results
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### 🌿 Plant Health Score")
        health_score = random.randint(75, 95)
        
        # Create a gauge chart
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = health_score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Health Score"},
            delta = {'reference': 80},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkgreen"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "yellow"},
                    {'range': [80, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        fig.update_layout(height=250)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("#### 🦠 Disease Detection")
        diseases = [
            {"name": "Leaf Spot", "confidence": 85, "severity": "Low"},
            {"name": "Powdery Mildew", "confidence": 12, "severity": "None"},
            {"name": "Rust", "confidence": 8, "severity": "None"}
        ]
        
        for disease in diseases:
            if disease["confidence"] > 50:
                st.error(f"⚠️ {disease['name']}: {disease['confidence']}% confidence")
            elif disease["confidence"] > 20:
                st.warning(f"⚡ {disease['name']}: {disease['confidence']}% confidence")
            else:
                st.success(f"✅ {disease['name']}: {disease['confidence']}% confidence")
    
    with col3:
        st.markdown("#### 💊 Treatment Recommendations")
        st.markdown("""
        **Immediate Actions:**
        - 🧪 Apply organic fungicide
        - 💧 Reduce watering frequency
        - 🌬️ Improve air circulation
        
        **Preventive Measures:**
        - 🕐 Morning watering only
        - 🍃 Remove affected leaves
        - 📏 Maintain plant spacing
        """)

# Historical monitoring
st.markdown("---")
st.markdown("### 📊 Plant Health History")

# Generate sample historical data
dates = pd.date_range(start='2024-08-01', end='2024-09-20', freq='D')
np.random.seed(42)

health_history = pd.DataFrame({
    'Date': dates,
    'Health_Score': np.random.normal(85, 8, len(dates)),
    'Disease_Risk': np.random.normal(15, 5, len(dates))
})

# Ensure values are within reasonable ranges
health_history['Health_Score'] = np.clip(health_history['Health_Score'], 60, 100)
health_history['Disease_Risk'] = np.clip(health_history['Disease_Risk'], 0, 40)

# Plot health trends
fig = px.line(health_history, x='Date', y=['Health_Score', 'Disease_Risk'], 
              title="Plant Health Trends Over Time",
              labels={'value': 'Score/Risk %', 'variable': 'Metric'})
fig.update_layout(height=400)
st.plotly_chart(fig, use_container_width=True)

# Disease library
st.markdown("---")
st.markdown("### 📚 Common Plant Diseases")

disease_tabs = st.tabs(["🍃 Leaf Diseases", "🌱 Root Issues", "🍎 Fruit Problems", "🐛 Pest Damage"])

with disease_tabs[0]:
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **Leaf Spot**
        - Dark spots on leaves
        - Caused by fungi
        - Treatment: Fungicide spray
        """)
    
    with col2:
        st.markdown("""
        **Powdery Mildew**
        - White powdery coating
        - High humidity cause
        - Treatment: Improve airflow
        """)
    
    with col3:
        st.markdown("""
        **Rust**
        - Orange/brown spots
        - Fungal infection
        - Treatment: Copper fungicide
        """)

with disease_tabs[1]:
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Root Rot**
        - Wilting despite moist soil
        - Dark, mushy roots
        - Treatment: Improve drainage
        """)
    
    with col2:
        st.markdown("""
        **Nutrient Deficiency**
        - Yellowing leaves
        - Stunted growth
        - Treatment: Balanced fertilizer
        """)

with disease_tabs[2]:
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Blossom End Rot**
        - Dark spots on fruit bottom
        - Calcium deficiency
        - Treatment: Consistent watering
        """)
    
    with col2:
        st.markdown("""
        **Fruit Cracking**
        - Splits in fruit skin
        - Irregular watering
        - Treatment: Mulching
        """)

with disease_tabs[3]:
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **Aphids**
        - Small green insects
        - Sticky honeydew
        - Treatment: Insecticidal soap
        """)
    
    with col2:
        st.markdown("""
        **Spider Mites**
        - Tiny webs on leaves
        - Stippled appearance
        - Treatment: Miticide spray
        """)
    
    with col3:
        st.markdown("""
        **Caterpillars**
        - Holes in leaves
        - Visible larvae
        - Treatment: Bt spray
        """)

# Action buttons
st.markdown("---")
st.markdown("### ⚡ Quick Actions")

col1, col2, col3, col4 = st.columns(4)

with col1:
    if st.button("📱 Schedule Monitoring", use_container_width=True):
        st.info("📅 Daily monitoring scheduled!")

with col2:
    if st.button("🚨 Set Disease Alerts", use_container_width=True):
        st.info("🔔 Disease alerts activated!")

with col3:
    if st.button("📊 Generate Report", use_container_width=True):
        st.info("📄 Plant health report generated!")

with col4:
    if st.button("🤖 AI Consultation", use_container_width=True):
        st.info("🧠 AI expert consultation requested!")

# Tips section
st.markdown("---")
st.markdown("### 💡 Pro Tips for Plant Monitoring")

tips_col1, tips_col2 = st.columns(2)

with tips_col1:
    st.markdown("""
    **📸 Photography Tips:**
    - Take photos in natural light
    - Capture both healthy and affected areas
    - Use macro mode for detailed shots
    - Document progression over time
    """)

with tips_col2:
    st.markdown("""
    **🔍 Monitoring Best Practices:**
    - Check plants daily during growing season
    - Look for early warning signs
    - Keep detailed records
    - Act quickly on disease detection
    """)
